# SchedSpot Testing Guide

## Role Switching & Worker Frontend Enhancement Testing

This guide covers testing the enhanced role switching functionality and comprehensive worker frontend capabilities.

## Prerequisites

1. WordPress site with SchedSpot plugin activated
2. Admin user account
3. Test worker and customer accounts
4. Pages with `[schedspot_booking_form]` and `[schedspot_dashboard]` shortcodes

## 1. Role Switching Testing

### 1.1 Admin Role Switcher Access
1. Log in as administrator
2. Navigate to **SchedSpot > Role Switcher** in admin menu
3. Verify current role mode is displayed
4. Test switching to different roles:
   - Worker View
   - Customer View
   - Administrator (reset)

### 1.2 Admin Bar Quick Switcher
1. Check admin bar for "SchedSpot: [Current Role]" menu
2. Test quick switching between roles
3. Verify role changes persist across page loads
4. Test reset to administrator functionality

### 1.3 Frontend Role Detection
1. Switch to Worker View in admin
2. Visit page with `[schedspot_dashboard]` shortcode
3. Verify worker dashboard is displayed
4. Check for "Admin Mode" indicator
5. Repeat for Customer View

### 1.4 User Impersonation
1. In Role Switcher, select a test user to impersonate
2. Switch to their role
3. Verify dashboard shows impersonated user's data
4. Check navigation shows correct user name

## 2. Enhanced Worker Frontend Testing

### 2.1 Navigation Testing
1. Visit booking form page while logged in
2. Verify navigation bar appears with:
   - Book Service (active)
   - Dashboard
   - Admin switcher (if admin)
3. Click Dashboard link and verify navigation
4. From dashboard, click Book Service and verify return navigation

### 2.2 Worker Dashboard Access
1. Log in as worker or switch to worker role
2. Visit dashboard page
3. Verify worker-specific interface displays:
   - Statistics overview
   - Recent bookings
   - Quick actions
   - Settings button

### 2.3 Worker Settings Modal
1. Click "Settings" in navigation or "Manage Profile" button
2. Verify settings modal opens with tabs:
   - Profile
   - Schedule
   - Services
   - Payments
   - Service Areas (if geofencing enabled)

### 2.4 Profile Management Testing
1. Open Profile tab in settings
2. Test editing:
   - Bio text
   - Skills list
   - Hourly rate
   - Phone number
   - Certifications
   - Availability toggle
3. Save changes and verify persistence
4. Reload page and check data is retained

### 2.5 Schedule Management Testing
1. Open Schedule tab in settings
2. Test weekly schedule editor:
   - Enable/disable days
   - Set start/end times
   - Verify time validation
3. Save schedule and verify API response
4. Test schedule persistence

### 2.6 Service Management Testing
1. Open Services tab in settings
2. Verify available services are listed
3. Test enabling/disabling services
4. Test custom pricing for services
5. Save changes and verify API response

### 2.7 Payment Settings Testing
1. Open Payments tab in settings
2. Test payment preferences:
   - Payout method selection
   - Payout email
   - Tax ID entry
3. Verify earnings summary displays:
   - Total earnings
   - Pending payout
   - Commission rate
4. Save settings and verify persistence

## 3. API Endpoint Testing

### 3.1 Worker Profile API
- GET `/wp-json/schedspot/v1/workers/{id}/profile`
- PUT `/wp-json/schedspot/v1/workers/{id}/profile`

### 3.2 Worker Availability API
- GET `/wp-json/schedspot/v1/workers/{id}/availability`
- PUT `/wp-json/schedspot/v1/workers/{id}/availability`

### 3.3 Worker Services API
- GET `/wp-json/schedspot/v1/workers/{id}/services`
- PUT `/wp-json/schedspot/v1/workers/{id}/services`

### 3.4 Worker Payment Settings API
- GET `/wp-json/schedspot/v1/workers/{id}/payment-settings`
- PUT `/wp-json/schedspot/v1/workers/{id}/payment-settings`

### 3.5 Payment Processing API
- POST `/wp-json/schedspot/v1/payments/create-order`
- POST `/wp-json/schedspot/v1/payments/process`
- GET `/wp-json/schedspot/v1/payments/orders/{id}`

### 3.6 Messaging API
- GET `/wp-json/schedspot/v1/messages`
- POST `/wp-json/schedspot/v1/messages`
- GET `/wp-json/schedspot/v1/conversations/{user_id}`

## 4. Mobile Responsiveness Testing

### 4.1 Navigation on Mobile
1. Test on mobile device or browser dev tools
2. Verify navigation stacks vertically
3. Test modal responsiveness
4. Check settings tabs adapt to mobile layout

### 4.2 Settings Modal on Mobile
1. Open worker settings on mobile
2. Verify tabs become horizontal scrollable
3. Test form inputs are properly sized
4. Verify modal is scrollable

## 5. Error Handling Testing

### 5.1 API Error Handling
1. Test with invalid data
2. Verify error messages display properly
3. Test network failure scenarios
4. Check permission denied scenarios

### 5.2 Role Switching Edge Cases
1. Test switching while on frontend pages
2. Test with invalid role parameters
3. Test session persistence across browser restart
4. Test multiple admin users switching simultaneously

## 6. Performance Testing

### 6.1 Frontend Loading
1. Measure page load times with enhanced CSS
2. Test JavaScript execution performance
3. Verify no console errors
4. Check for memory leaks in modal operations

### 6.2 API Performance
1. Test API response times
2. Verify proper caching
3. Test with large datasets
4. Check database query efficiency

## 7. Security Testing

### 7.1 Permission Checks
1. Test API endpoints without proper permissions
2. Verify role switching requires admin capabilities
3. Test cross-user data access prevention
4. Check nonce validation

### 7.2 Data Sanitization
1. Test form inputs with malicious data
2. Verify proper escaping in output
3. Test SQL injection prevention
4. Check XSS prevention

## Expected Results

### ✅ Successful Tests Should Show:
- Role switching works seamlessly across frontend and backend
- Worker settings are fully functional from frontend
- Navigation between interfaces is intuitive
- All API endpoints respond correctly
- Mobile interface is fully responsive
- Security measures prevent unauthorized access
- Performance is acceptable under normal load

### ❌ Issues to Report:
- Role switching not persisting
- Settings not saving properly
- Navigation links broken
- API 404 or 500 errors
- Mobile layout issues
- Security vulnerabilities
- Performance degradation

## Troubleshooting

### Common Issues:
1. **Role switching not working**: Check user capabilities and nonce validation
2. **Settings not saving**: Verify API endpoints are registered and accessible
3. **Navigation broken**: Check page meta marking and URL generation
4. **Mobile issues**: Verify CSS media queries and responsive design
5. **API errors**: Check error logs and endpoint registration

### Debug Steps:
1. Enable WordPress debug mode
2. Check browser console for JavaScript errors
3. Monitor network tab for API calls
4. Review server error logs
5. Test with different user roles and permissions
